// 配置模块入口
export * from './types';
export * from './manager';

// 便捷函数
import { configManager } from './manager';

/**
 * 获取当前配置
 */
export const getConfig = () => configManager.getConfig();

/**
 * 获取通用配置
 */
export const getGeneralConfig = () => configManager.getGeneralConfig();

/**
 * 获取 AI 配置
 */
export const getAIConfig = () => configManager.getAIConfig();

/**
 * 获取其他配置
 */
export const getOtherConfig = () => configManager.getOtherConfig();

/**
 * 更新配置
 */
export const updateConfig = (config: Parameters<typeof configManager.updateConfig>[0]) => 
  configManager.updateConfig(config);

/**
 * 重新加载配置
 */
export const reloadConfig = () => configManager.reloadConfig();

/**
 * 检查配置文件是否存在
 */
export const configExists = () => configManager.configExists();

/**
 * 创建默认配置文件
 */
export const createDefaultConfig = () => configManager.createDefaultConfig();

/**
 * 验证配置
 */
export const validateConfig = (config: unknown) => configManager.validateConfig(config);
