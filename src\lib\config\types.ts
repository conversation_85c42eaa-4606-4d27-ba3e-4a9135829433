// 配置文件类型定义
import { z } from 'zod';
import type { AvailableLanguage } from 'tmdb-ts';

// TMDB 支持的语言列表（从 tmdb-ts 导入的类型转换为 Zod 枚举）
const TMDB_LANGUAGES = [
  'af-ZA', 'ar-AE', 'ar-BH', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-LY', 'ar-MA', 'ar-QA', 'ar-SA',
  'ar-TD', 'ar-YE', 'be-BY', 'bg-BG', 'bn-BD', 'br-FR', 'ca-AD', 'ca-ES', 'ch-GU', 'cs-CZ',
  'cy-GB', 'da-DK', 'de-AT', 'de-CH', 'de-DE', 'el-CY', 'el-GR', 'en-AG', 'en-AU', 'en-BB',
  'en-BZ', 'en-CA', 'en-CM', 'en-GB', 'en-GG', 'en-GH', 'en-GI', 'en-GY', 'en-IE', 'en-JM',
  'en-KE', 'en-LC', 'en-MW', 'en-NZ', 'en-PG', 'en-TC', 'en-US', 'en-ZM', 'en-ZW', 'eo-EO',
  'es-AR', 'es-CL', 'es-DO', 'es-EC', 'es-ES', 'es-GQ', 'es-GT', 'es-HN', 'es-MX', 'es-NI',
  'es-PA', 'es-PE', 'es-PY', 'es-SV', 'es-UY', 'et-EE', 'eu-ES', 'fa-IR', 'fi-FI', 'fr-BF',
  'fr-CA', 'fr-CD', 'fr-CI', 'fr-FR', 'fr-GF', 'fr-GP', 'fr-MC', 'fr-ML', 'fr-MU', 'fr-PF',
  'ga-IE', 'gd-GB', 'gl-ES', 'he-IL', 'hi-IN', 'hr-HR', 'hu-HU', 'id-ID', 'it-IT', 'it-VA',
  'ja-JP', 'ka-GE', 'kk-KZ', 'kn-IN', 'ko-KR', 'ky-KG', 'lt-LT', 'lv-LV', 'ml-IN', 'mr-IN',
  'ms-MY', 'ms-SG', 'nb-NO', 'nl-BE', 'nl-NL', 'no-NO', 'pa-IN', 'pl-PL', 'pt-AO', 'pt-BR',
  'pt-MZ', 'pt-PT', 'ro-MD', 'ro-RO', 'ru-RU', 'si-LK', 'sk-SK', 'sl-SI', 'sq-AL', 'sq-XK',
  'sr-ME', 'sr-RS', 'sv-SE', 'sw-TZ', 'ta-IN', 'te-IN', 'th-TH', 'tl-PH', 'tr-TR', 'uk-UA',
  'ur-PK', 'vi-VN', 'zh-CN', 'zh-HK', 'zh-SG', 'zh-TW', 'zu-ZA'
] as const;

// TMDB 语言 Schema
export const TmdbLanguageSchema = z.enum(TMDB_LANGUAGES);
export type TmdbLanguage = z.infer<typeof TmdbLanguageSchema>;

// 文件操作类型
export const FileOperationSchema = z.enum(['hardlink', 'softlink', 'copy', 'move', 'skip']);
export type FileOperation = z.infer<typeof FileOperationSchema>;

// 媒体类型
export const MediaTypeSchema = z.enum(['anime', 'tv', 'movie', 'anime_movie']);
export type MediaType = z.infer<typeof MediaTypeSchema>;

// AI 提供商类型
export const AIProviderSchema = z.enum(['openai', 'gemini', 'claude']);
export type AIProvider = z.infer<typeof AIProviderSchema>;

// 通用配置
export const GeneralConfigSchema = z.object({
  // TMDB 配置
  tmdb: z.object({
    apiKey: z.string().min(1, 'TMDB API Key 不能为空'),
    language: TmdbLanguageSchema.default('zh-CN'),
    region: z.string().default('CN'),
  }),
  
  // 路径配置
  paths: z.object({
    // 输出根目录
    outputRoot: z.string().min(1, '输出根目录不能为空'),
    // 临时目录（用于移动操作的中间存储）
    tempDir: z.string().optional(),
  }),
  
  // 默认设置
  defaults: z.object({
    // 默认文件操作类型
    fileOperation: FileOperationSchema.default('copy'),
    // 默认媒体类型
    mediaType: MediaTypeSchema.default('anime'),
    // 是否启用 AI 模式
    enableAI: z.boolean().default(false),
  }),
});

// AI 配置
export const AIConfigSchema = z.object({
  // AI 提供商
  provider: AIProviderSchema.default('openai'),
  
  // OpenAI 配置
  openai: z.object({
    apiKey: z.string().optional(),
    model: z.string().default('gpt-4o-mini'),
    temperature: z.number().min(0).max(2).default(0.1),
    maxTokens: z.number().min(1).default(4000),
  }),
  
  // Gemini 配置
  gemini: z.object({
    apiKey: z.string().optional(),
    model: z.string().default('gemini-1.5-flash'),
    temperature: z.number().min(0).max(2).default(0.1),
    maxTokens: z.number().min(1).default(4000),
  }),
  
  // Claude 配置
  claude: z.object({
    apiKey: z.string().optional(),
    baseURL: z.url().optional(),
    model: z.string().default('claude-3-haiku-20240307'),
    temperature: z.number().min(0).max(1).default(0.1),
    maxTokens: z.number().min(1).default(4000),
  }),
  
  // AI 分析配置
  analysis: z.object({
    // 置信度阈值（低于此值需要用户确认）
    confidenceThreshold: z.number().min(0).max(1).default(0.8),
    // 是否启用电影分离功能
    enableMovieSeparation: z.boolean().default(true),
    // 自定义 Prompt（可选）
    customPrompt: z.string().optional(),
  }),
});

// 其他配置（预留）
export const OtherConfigSchema = z.object({
  // 日志配置
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
    enableFileLogging: z.boolean().default(true),
    maxLogFiles: z.number().min(1).default(10),
  }),
  
  // 性能配置
  performance: z.object({
    // 并发文件处理数量
    maxConcurrentFiles: z.number().min(1).default(5),
    // 任务队列最大长度
    maxQueueLength: z.number().min(1).default(100),
  }),
  
  // 实验性功能
  experimental: z.object({
    // 启用字幕整理
    enableSubtitleOrganization: z.boolean().default(false),
    // 启用预告片整理
    enableTrailerOrganization: z.boolean().default(false),
    // 启用字体文件收集
    enableFontCollection: z.boolean().default(false),
  }),
});

// 完整配置 Schema
export const ConfigSchema = z.object({
  general: GeneralConfigSchema,
  ai: AIConfigSchema,
  other: OtherConfigSchema,
});

export type Config = z.infer<typeof ConfigSchema>;
export type GeneralConfig = z.infer<typeof GeneralConfigSchema>;
export type AIConfig = z.infer<typeof AIConfigSchema>;
export type OtherConfig = z.infer<typeof OtherConfigSchema>;

// 默认配置
export const DEFAULT_CONFIG: Config = {
  general: {
    tmdb: {
      apiKey: '',
      language: 'zh-CN' as TmdbLanguage,
      region: 'CN',
    },
    paths: {
      outputRoot: '',
    },
    defaults: {
      fileOperation: 'copy',
      mediaType: 'anime',
      enableAI: false,
    },
  },
  ai: {
    provider: 'openai',
    openai: {
      model: 'gpt-4o-mini',
      temperature: 0.1,
      maxTokens: 4000,
    },
    gemini: {
      model: 'gemini-1.5-flash',
      temperature: 0.1,
      maxTokens: 4000,
    },
    claude: {
      model: 'claude-3-haiku-20240307',
      temperature: 0.1,
      maxTokens: 4000,
    },
    analysis: {
      confidenceThreshold: 0.8,
      enableMovieSeparation: true,
    },
  },
  other: {
    logging: {
      level: 'info',
      enableFileLogging: true,
      maxLogFiles: 10,
    },
    performance: {
      maxConcurrentFiles: 5,
      maxQueueLength: 100,
    },
    experimental: {
      enableSubtitleOrganization: false,
      enableTrailerOrganization: false,
      enableFontCollection: false,
    },
  },
};
